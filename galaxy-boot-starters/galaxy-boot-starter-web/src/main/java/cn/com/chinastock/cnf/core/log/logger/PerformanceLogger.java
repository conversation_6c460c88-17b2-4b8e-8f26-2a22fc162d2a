package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.util.StopWatch;

/**
 * PerformanceLogger 类用于记录方法的执行时间，帮助监控和优化性能。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #PerformanceLogger()}：构造函数，初始化日志配置属性。</li>
 *     <li>{@link #start()}：启动性能监控。</li>
 *     <li>{@link #stop()}：停止性能监控并记录耗时。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class PerformanceLogger {
    private final ThreadLocal<StopWatch> stopWatchThreadLocal = ThreadLocal.withInitial(StopWatch::new);
    private final ThreadLocal<String> spanIdThreadLocal = new ThreadLocal<>();
    private final ThreadLocal<String> traceIdThreadLocal = new ThreadLocal<>();

    /**
     * 构造函数
     *
     */
    public PerformanceLogger() {
    }

    /**
     * 启动性能监控
     *
     * <p>如果StopWatch已经在运行状态，会先停止当前计时再重新开始，
     * 确保每次调用都能正常启动计时。</p>
     */
    public void start() {
        try {
            StopWatch stopWatch = stopWatchThreadLocal.get();

            // 如果StopWatch已经在运行，先停止它
            if (stopWatch.isRunning()) {
                GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG,
                    "StopWatch is already running, stopping it before restart");
                stopWatch.stop();
            }

            // 如果StopWatch已经有任务记录，创建新的实例
            if (stopWatch.getTaskCount() > 0) {
                stopWatch = new StopWatch();
                stopWatchThreadLocal.set(stopWatch);
            }

            // 保存当前的traceId和spanId，确保性能日志使用一致的trace信息
            saveCurrentTraceContext();

            stopWatch.start();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                "Failed to start performance watch", e);
            // 重置StopWatch状态
            resetStopWatch();
        }
    }

    /**
     * 停止性能监控并记录耗时
     *
     * <p>如果StopWatch没有在运行状态，会跳过停止操作，
     * 确保不会因为状态异常而影响正常业务逻辑。</p>
     */
    public void stop() {
        try {
            StopWatch stopWatch = stopWatchThreadLocal.get();

            // 检查StopWatch是否在运行状态
            if (!stopWatch.isRunning()) {
                GalaxyLogger.warn(LogCategory.FRAMEWORK_LOG,
                    "StopWatch is not running, skipping stop operation");
                return;
            }

            stopWatch.stop();

            // 使用保存的trace上下文记录性能日志，确保spanId一致性
            logPerformanceWithSavedContext(stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                "Failed to stop performance watch", e);
        } finally {
            // 确保清理ThreadLocal，避免内存泄漏
            cleanupThreadLocals();
        }
    }

    /**
     * 重置StopWatch状态
     *
     * <p>在异常情况下重置StopWatch状态，确保下次能正常使用。
     * 此方法可以从外部调用，用于处理异常情况下的状态清理。</p>
     */
    public void resetStopWatch() {
        try {
            cleanupThreadLocals();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                "Failed to reset StopWatch", e);
        }
    }

    /**
     * 保存当前的trace上下文信息
     */
    private void saveCurrentTraceContext() {
        try {
            // 从MDC中获取当前的traceId和spanId
            String currentTraceId = org.slf4j.MDC.get("traceId");
            String currentSpanId = org.slf4j.MDC.get("spanId");

            if (currentTraceId != null) {
                traceIdThreadLocal.set(currentTraceId);
            }
            if (currentSpanId != null) {
                spanIdThreadLocal.set(currentSpanId);
            }
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG,
                "Failed to save current trace context", e);
        }
    }

    /**
     * 使用保存的trace上下文记录性能日志
     */
    private void logPerformanceWithSavedContext(long costMillis) {
        String savedTraceId = traceIdThreadLocal.get();
        String savedSpanId = spanIdThreadLocal.get();

        if (savedTraceId != null && savedSpanId != null) {
            // 临时设置MDC为保存的值
            String originalTraceId = org.slf4j.MDC.get("traceId");
            String originalSpanId = org.slf4j.MDC.get("spanId");

            try {
                org.slf4j.MDC.put("traceId", savedTraceId);
                org.slf4j.MDC.put("spanId", savedSpanId);

                GalaxyLogger.info(LogCategory.PERFORMANCE_LOG,
                        "cost=" + costMillis + " unit=ms");
            } finally {
                // 恢复原始的MDC值
                if (originalTraceId != null) {
                    org.slf4j.MDC.put("traceId", originalTraceId);
                } else {
                    org.slf4j.MDC.remove("traceId");
                }
                if (originalSpanId != null) {
                    org.slf4j.MDC.put("spanId", originalSpanId);
                } else {
                    org.slf4j.MDC.remove("spanId");
                }
            }
        } else {
            // 如果没有保存的trace信息，直接记录
            GalaxyLogger.info(LogCategory.PERFORMANCE_LOG,
                    "cost=" + costMillis + " unit=ms");
        }
    }

    /**
     * 清理所有ThreadLocal变量
     */
    private void cleanupThreadLocals() {
        stopWatchThreadLocal.remove();
        spanIdThreadLocal.remove();
        traceIdThreadLocal.remove();
    }
} 